{"name": "stakeholder-request-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:push": "prisma db push", "db:seed": "prisma db seed", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "docker:dev": "docker-compose -f docker/docker-compose.yml up -d", "docker:down": "docker-compose -f docker/docker-compose.yml down", "docker:logs": "docker-compose -f docker/docker-compose.yml logs -f", "setup": "npm install && npm run db:generate && npm run docker:dev && npm run db:migrate", "clean": "rm -rf .next node_modules/.cache", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@hookform/resolvers": "^5.2.1", "@prisma/client": "^6.13.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.535.0", "next": "15.4.5", "next-auth": "^4.24.11", "prisma": "^6.13.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "tailwind-merge": "^3.3.1", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.54.2", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/amqplib": "^0.10.7", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "amqplib": "^0.10.8", "bcryptjs": "^3.0.2", "eslint": "^9", "eslint-config-next": "15.4.5", "ioredis": "^5.7.0", "jest": "^29.7.0", "redis": "^5.7.0", "tailwindcss": "^4", "typescript": "^5"}}
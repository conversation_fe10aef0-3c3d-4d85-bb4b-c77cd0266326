version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: stakeholder-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: stakeholder_requests
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - stakeholder-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d stakeholder_requests"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: stakeholder-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_password
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - stakeholder-network
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "redis_password", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  # RabbitMQ Message Broker
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: stakeholder-rabbitmq
    restart: unless-stopped
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin_password
      RABBITMQ_DEFAULT_VHOST: stakeholder_vhost
    ports:
      - "5672:5672"   # AMQP port
      - "15672:15672" # Management UI
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - ./rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
    networks:
      - stakeholder-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 15s

  # Adminer - Database Management UI
  adminer:
    image: adminer:4-standalone
    container_name: stakeholder-adminer
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      ADMINER_DEFAULT_SERVER: postgres
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - stakeholder-network

  # Mailhog - Email Testing (Development)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: stakeholder-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025" # SMTP port
      - "8025:8025" # Web UI
    networks:
      - stakeholder-network

  # MinIO - S3-compatible Object Storage (Development)
  minio:
    image: minio/minio:latest
    container_name: stakeholder-minio
    restart: unless-stopped
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000" # API port
      - "9001:9001" # Console port
    volumes:
      - minio_data:/data
    networks:
      - stakeholder-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  rabbitmq_data:
    driver: local
  minio_data:
    driver: local

networks:
  stakeholder-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

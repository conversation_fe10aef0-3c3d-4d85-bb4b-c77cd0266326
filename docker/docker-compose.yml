version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: stakeholder-postgres
    environment:
      POSTGRES_DB: stakeholder_requests
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - stakeholder-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: stakeholder-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - stakeholder-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RabbitMQ Message Broker
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: stakeholder-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin
    ports:
      - "5672:5672"   # AMQP port
      - "15672:15672" # Management UI
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - stakeholder-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Next.js Application (Development)
  app:
    build:
      context: ..
      dockerfile: docker/Dockerfile.dev
    container_name: stakeholder-app
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/stakeholder_requests?schema=public
      - REDIS_URL=redis://redis:6379
      - RABBITMQ_URL=amqp://admin:admin@rabbitmq:5672
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=development-secret-key
    volumes:
      - ../src:/app/src
      - ../public:/app/public
      - ../prisma:/app/prisma
      - ../package.json:/app/package.json
      - ../package-lock.json:/app/package-lock.json
      - ../tsconfig.json:/app/tsconfig.json
      - ../next.config.ts:/app/next.config.ts
      - ../tailwind.config.ts:/app/tailwind.config.ts
      - ../postcss.config.mjs:/app/postcss.config.mjs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - stakeholder-network
    command: npm run dev

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:

networks:
  stakeholder-network:
    driver: bridge

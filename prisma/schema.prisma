// Stakeholder Request Management System - Database Schema
// This schema defines the data model for managing feature requests and changes

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management and Authentication
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String?
  image         String?
  password      String?   // For credentials-based authentication
  role          UserR<PERSON>  @default(END_USER)
  department    String?
  isActive      Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Authentication
  accounts      Account[]
  sessions      Session[]

  // Request Management
  submittedRequests  Request[] @relation("RequestSubmitter")
  assignedRequests   Request[] @relation("RequestAssignee")
  comments           Comment[]
  attachments        Attachment[]
  
  // Activity Tracking
  activities         Activity[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

// Request Management Core Models
model Request {
  id              String        @id @default(cuid())
  title           String
  description     String        @db.Text
  type            RequestType
  priority        Priority      @default(MEDIUM)
  status          RequestStatus @default(SUBMITTED)
  urgency         Urgency       @default(NORMAL)
  
  // Business Context
  businessValue   Int?          // 1-10 scale
  effort          Int?          // Story points or hours
  riskLevel       RiskLevel     @default(LOW)
  
  // Stakeholder Information
  submitterId     String
  submitter       User          @relation("RequestSubmitter", fields: [submitterId], references: [id])
  assigneeId      String?
  assignee        User?         @relation("RequestAssignee", fields: [assigneeId], references: [id])
  
  // Categorization
  category        Category?     @relation(fields: [categoryId], references: [id])
  categoryId      String?
  tags            Tag[]
  
  // Dates and Timeline
  submittedAt     DateTime      @default(now())
  dueDate         DateTime?
  startedAt       DateTime?
  completedAt     DateTime?
  deployedAt      DateTime?
  
  // Metadata
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  
  // Related Data
  comments        Comment[]
  attachments     Attachment[]
  activities      Activity[]
  integrations    Integration[]

  @@map("requests")
}

model Category {
  id          String    @id @default(cuid())
  name        String    @unique
  description String?
  color       String?   // Hex color for UI
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  requests    Request[]

  @@map("categories")
}

model Tag {
  id          String    @id @default(cuid())
  name        String    @unique
  color       String?   // Hex color for UI
  createdAt   DateTime  @default(now())
  
  requests    Request[]

  @@map("tags")
}

model Comment {
  id          String    @id @default(cuid())
  content     String    @db.Text
  isInternal  Boolean   @default(false) // Internal comments vs public
  
  // Relations
  requestId   String
  request     Request   @relation(fields: [requestId], references: [id], onDelete: Cascade)
  authorId    String
  author      User      @relation(fields: [authorId], references: [id])
  
  // Metadata
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("comments")
}

model Attachment {
  id          String    @id @default(cuid())
  filename    String
  originalName String
  mimeType    String
  size        Int       // File size in bytes
  url         String    // Storage URL (S3, etc.)
  
  // Relations
  requestId   String?
  request     Request?  @relation(fields: [requestId], references: [id], onDelete: Cascade)
  uploaderId  String
  uploader    User      @relation(fields: [uploaderId], references: [id])
  
  // Metadata
  createdAt   DateTime  @default(now())

  @@map("attachments")
}

// Activity and Audit Trail
model Activity {
  id          String       @id @default(cuid())
  action      ActivityType
  description String
  metadata    Json?        // Additional context data
  
  // Relations
  requestId   String?
  request     Request?     @relation(fields: [requestId], references: [id], onDelete: Cascade)
  userId      String
  user        User         @relation(fields: [userId], references: [id])
  
  // Metadata
  createdAt   DateTime     @default(now())

  @@map("activities")
}

// Integration with External Systems
model Integration {
  id            String           @id @default(cuid())
  type          IntegrationType
  externalId    String           // ID in external system (Jira ticket, etc.)
  externalUrl   String?          // Link to external system
  status        IntegrationStatus @default(ACTIVE)
  metadata      Json?            // Additional integration data
  
  // Relations
  requestId     String
  request       Request          @relation(fields: [requestId], references: [id], onDelete: Cascade)
  
  // Metadata
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt
  lastSyncAt    DateTime?

  @@unique([type, externalId])
  @@map("integrations")
}

// Enums
enum UserRole {
  ADMIN
  PRODUCT_MANAGER
  DEVELOPER
  QA
  OPERATIONS
  SUPPORT
  MANAGEMENT
  END_USER
  CUSTOMER
}

enum RequestType {
  FEATURE
  BUG_FIX
  ENHANCEMENT
  TECHNICAL_DEBT
  DOCUMENTATION
  INFRASTRUCTURE
  SECURITY
  PERFORMANCE
  INTEGRATION
  OTHER
}

enum Priority {
  CRITICAL
  HIGH
  MEDIUM
  LOW
}

enum Urgency {
  IMMEDIATE
  HIGH
  NORMAL
  LOW
}

enum RequestStatus {
  SUBMITTED
  UNDER_REVIEW
  APPROVED
  REJECTED
  IN_PROGRESS
  IN_TESTING
  READY_FOR_DEPLOYMENT
  DEPLOYED
  CLOSED
  CANCELLED
}

enum RiskLevel {
  CRITICAL
  HIGH
  MEDIUM
  LOW
}

enum ActivityType {
  CREATED
  UPDATED
  STATUS_CHANGED
  ASSIGNED
  COMMENTED
  ATTACHED_FILE
  PRIORITY_CHANGED
  DUE_DATE_CHANGED
  INTEGRATED
  DEPLOYED
}

enum IntegrationType {
  JIRA
  CONFLUENCE
  GITHUB
  GITLAB
  SLACK
  TEAMS
  WEBHOOK
}

enum IntegrationStatus {
  ACTIVE
  INACTIVE
  ERROR
  PENDING
}

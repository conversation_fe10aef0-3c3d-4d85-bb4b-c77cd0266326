import amqp, { Connection, Channel } from 'amqplib'

class RabbitMQService {
  private connection: Connection | null = null
  private channel: Channel | null = null
  private readonly url: string

  constructor() {
    this.url = process.env.RABBITMQ_URL || 'amqp://localhost:5672'
  }

  async connect(): Promise<void> {
    try {
      this.connection = await amqp.connect(this.url)
      this.channel = await this.connection.createChannel()
      
      // Setup error handlers
      this.connection.on('error', (err) => {
        console.error('RabbitMQ connection error:', err)
      })
      
      this.connection.on('close', () => {
        console.log('RabbitMQ connection closed')
      })
      
      console.log('Connected to RabbitMQ')
    } catch (error) {
      console.error('Failed to connect to RabbitMQ:', error)
      throw error
    }
  }

  async disconnect(): Promise<void> {
    try {
      if (this.channel) {
        await this.channel.close()
      }
      if (this.connection) {
        await this.connection.close()
      }
    } catch (error) {
      console.error('Error disconnecting from RabbitMQ:', error)
    }
  }

  async ensureQueue(queueName: string, options: any = {}): Promise<void> {
    if (!this.channel) {
      throw new Error('RabbitMQ channel not initialized')
    }
    
    await this.channel.assertQueue(queueName, {
      durable: true,
      ...options
    })
  }

  async publishToQueue(queueName: string, message: any): Promise<boolean> {
    try {
      if (!this.channel) {
        await this.connect()
      }
      
      await this.ensureQueue(queueName)
      
      const messageBuffer = Buffer.from(JSON.stringify(message))
      return this.channel!.sendToQueue(queueName, messageBuffer, {
        persistent: true
      })
    } catch (error) {
      console.error('Error publishing to queue:', error)
      return false
    }
  }

  async consumeFromQueue(
    queueName: string, 
    callback: (message: any) => Promise<void>
  ): Promise<void> {
    try {
      if (!this.channel) {
        await this.connect()
      }
      
      await this.ensureQueue(queueName)
      
      await this.channel!.consume(queueName, async (msg) => {
        if (msg) {
          try {
            const content = JSON.parse(msg.content.toString())
            await callback(content)
            this.channel!.ack(msg)
          } catch (error) {
            console.error('Error processing message:', error)
            this.channel!.nack(msg, false, false) // Don't requeue failed messages
          }
        }
      })
    } catch (error) {
      console.error('Error consuming from queue:', error)
      throw error
    }
  }
}

// Singleton instance
const rabbitmq = new RabbitMQService()

// Queue names
export const QUEUES = {
  EMAIL_NOTIFICATIONS: 'email_notifications',
  REQUEST_UPDATES: 'request_updates',
  INTEGRATION_SYNC: 'integration_sync',
  ANALYTICS_EVENTS: 'analytics_events',
  FILE_PROCESSING: 'file_processing'
} as const

// Message types
export interface EmailNotificationMessage {
  type: 'email_notification'
  to: string
  subject: string
  template: string
  data: Record<string, any>
}

export interface RequestUpdateMessage {
  type: 'request_update'
  requestId: string
  action: string
  userId: string
  data: Record<string, any>
}

export interface IntegrationSyncMessage {
  type: 'integration_sync'
  requestId: string
  integrationType: string
  action: 'create' | 'update' | 'sync'
  data: Record<string, any>
}

export interface AnalyticsEventMessage {
  type: 'analytics_event'
  event: string
  userId?: string
  requestId?: string
  metadata: Record<string, any>
}

export interface FileProcessingMessage {
  type: 'file_processing'
  fileId: string
  action: 'scan' | 'process' | 'cleanup'
  metadata: Record<string, any>
}

// Utility functions
export const messageQueue = {
  async sendEmailNotification(message: EmailNotificationMessage): Promise<boolean> {
    return rabbitmq.publishToQueue(QUEUES.EMAIL_NOTIFICATIONS, message)
  },

  async sendRequestUpdate(message: RequestUpdateMessage): Promise<boolean> {
    return rabbitmq.publishToQueue(QUEUES.REQUEST_UPDATES, message)
  },

  async sendIntegrationSync(message: IntegrationSyncMessage): Promise<boolean> {
    return rabbitmq.publishToQueue(QUEUES.INTEGRATION_SYNC, message)
  },

  async sendAnalyticsEvent(message: AnalyticsEventMessage): Promise<boolean> {
    return rabbitmq.publishToQueue(QUEUES.ANALYTICS_EVENTS, message)
  },

  async sendFileProcessing(message: FileProcessingMessage): Promise<boolean> {
    return rabbitmq.publishToQueue(QUEUES.FILE_PROCESSING, message)
  }
}

export default rabbitmq

import { 
  User, 
  Request, 
  Comment, 
  Attachment, 
  Category, 
  Tag, 
  Activity, 
  Integration,
  UserRole,
  RequestType,
  Priority,
  Urgency,
  RequestStatus,
  RiskLevel,
  ActivityType,
  IntegrationType,
  IntegrationStatus
} from '@prisma/client'

// Extended types with relations
export interface UserWithRelations extends User {
  submittedRequests?: RequestWithRelations[]
  assignedRequests?: RequestWithRelations[]
  comments?: Comment[]
  attachments?: Attachment[]
  activities?: Activity[]
}

export interface RequestWithRelations extends Request {
  submitter: User
  assignee?: User | null
  category?: Category | null
  tags: Tag[]
  comments: CommentWithAuthor[]
  attachments: AttachmentWithUploader[]
  activities: ActivityWithUser[]
  integrations: Integration[]
}

export interface CommentWithAuthor extends Comment {
  author: User
}

export interface AttachmentWithUploader extends Attachment {
  uploader: User
}

export interface ActivityWithUser extends Activity {
  user: User
}

// Form types
export interface CreateRequestForm {
  title: string
  description: string
  type: RequestType
  priority: Priority
  urgency: Urgency
  businessValue?: number
  effort?: number
  riskLevel: RiskLevel
  categoryId?: string
  tags: string[]
  dueDate?: Date
  attachments?: File[]
}

export interface UpdateRequestForm extends Partial<CreateRequestForm> {
  id: string
  status?: RequestStatus
  assigneeId?: string
}

export interface CreateCommentForm {
  content: string
  isInternal: boolean
  requestId: string
}

export interface CreateCategoryForm {
  name: string
  description?: string
  color?: string
}

export interface CreateTagForm {
  name: string
  color?: string
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// Filter and search types
export interface RequestFilters {
  status?: RequestStatus[]
  type?: RequestType[]
  priority?: Priority[]
  urgency?: Urgency[]
  assigneeId?: string[]
  submitterId?: string[]
  categoryId?: string[]
  tags?: string[]
  dateRange?: {
    from: Date
    to: Date
  }
  search?: string
}

export interface RequestSortOptions {
  field: 'createdAt' | 'updatedAt' | 'priority' | 'dueDate' | 'title'
  direction: 'asc' | 'desc'
}

// Dashboard and analytics types
export interface DashboardStats {
  totalRequests: number
  openRequests: number
  inProgressRequests: number
  completedRequests: number
  overdueRequests: number
  requestsByStatus: Record<RequestStatus, number>
  requestsByType: Record<RequestType, number>
  requestsByPriority: Record<Priority, number>
  averageCompletionTime: number
  userActivity: {
    userId: string
    userName: string
    requestCount: number
  }[]
}

export interface AnalyticsData {
  requestTrends: {
    date: string
    count: number
    type: RequestType
  }[]
  completionRates: {
    period: string
    completed: number
    total: number
    rate: number
  }[]
  performanceMetrics: {
    averageResponseTime: number
    averageResolutionTime: number
    satisfactionScore: number
  }
}

// Notification types
export interface NotificationData {
  id: string
  type: 'request_created' | 'request_updated' | 'comment_added' | 'status_changed' | 'assignment_changed'
  title: string
  message: string
  requestId?: string
  userId: string
  read: boolean
  createdAt: Date
}

// Integration types
export interface JiraIntegrationConfig {
  baseUrl: string
  username: string
  apiToken: string
  projectKey: string
  issueType: string
}

export interface SlackIntegrationConfig {
  botToken: string
  signingSecret: string
  channelId: string
}

export interface WebhookIntegrationConfig {
  url: string
  secret?: string
  events: string[]
}

// File upload types
export interface FileUploadConfig {
  maxSize: number
  allowedTypes: string[]
  uploadPath: string
}

export interface UploadedFile {
  id: string
  filename: string
  originalName: string
  mimeType: string
  size: number
  url: string
}

// Theme and UI types
export interface ThemeConfig {
  primary: string
  secondary: string
  accent: string
  background: string
  foreground: string
  muted: string
  border: string
}

// Export all Prisma enums for convenience
export {
  UserRole,
  RequestType,
  Priority,
  Urgency,
  RequestStatus,
  RiskLevel,
  ActivityType,
  IntegrationType,
  IntegrationStatus
}

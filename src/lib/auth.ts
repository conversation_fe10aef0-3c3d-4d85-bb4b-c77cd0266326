import { NextAuthOptions } from "next-auth"
import { PrismaAdapter } from "@auth/prisma-adapter"
import GoogleProvider from "next-auth/providers/google"
import GitHubProvider from "next-auth/providers/github"
import Cred<PERSON><PERSON><PERSON>rovider from "next-auth/providers/credentials"
import bcrypt from "bcryptjs"
import { prisma } from "./db"
import { UserRole } from "@prisma/client"

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    GitHubProvider({
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
    }),
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email
          }
        })

        if (!user) {
          return null
        }

        // For OAuth users, password might not be set
        if (!user.password) {
          return null
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.password
        )

        if (!isPasswordValid) {
          return null
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          image: user.image,
          role: user.role,
        }
      }
    })
  ],
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, user, account }) {
      if (user) {
        token.role = user.role
        token.id = user.id
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string
        session.user.role = token.role as UserRole
      }
      return session
    },
    async signIn({ user, account, profile }) {
      if (account?.provider === "google" || account?.provider === "github") {
        try {
          const existingUser = await prisma.user.findUnique({
            where: { email: user.email! }
          })

          if (!existingUser) {
            // Create new user with default role
            await prisma.user.create({
              data: {
                email: user.email!,
                name: user.name,
                image: user.image,
                role: UserRole.END_USER, // Default role for new users
              }
            })
          }
        } catch (error) {
          console.error("Error during sign in:", error)
          return false
        }
      }
      return true
    }
  },
  pages: {
    signIn: "/auth/signin",
    signUp: "/auth/signup",
    error: "/auth/error",
  },
  events: {
    async signIn({ user, account, isNewUser }) {
      console.log(`User ${user.email} signed in with ${account?.provider}`)
      
      // Track sign-in activity
      if (user.id) {
        await prisma.activity.create({
          data: {
            action: "SIGN_IN",
            description: `User signed in with ${account?.provider}`,
            userId: user.id,
            metadata: {
              provider: account?.provider,
              isNewUser
            }
          }
        }).catch(console.error)
      }
    },
    async signOut({ session }) {
      console.log(`User ${session?.user?.email} signed out`)
    }
  },
  debug: process.env.NODE_ENV === "development",
}

// Helper functions for role-based access control
export const hasRole = (userRole: UserRole, requiredRoles: UserRole[]): boolean => {
  return requiredRoles.includes(userRole)
}

export const isAdmin = (userRole: UserRole): boolean => {
  return userRole === UserRole.ADMIN
}

export const canManageRequests = (userRole: UserRole): boolean => {
  return hasRole(userRole, [
    UserRole.ADMIN,
    UserRole.PRODUCT_MANAGER,
    UserRole.MANAGEMENT
  ])
}

export const canAssignRequests = (userRole: UserRole): boolean => {
  return hasRole(userRole, [
    UserRole.ADMIN,
    UserRole.PRODUCT_MANAGER,
    UserRole.MANAGEMENT
  ])
}

export const canViewAllRequests = (userRole: UserRole): boolean => {
  return hasRole(userRole, [
    UserRole.ADMIN,
    UserRole.PRODUCT_MANAGER,
    UserRole.MANAGEMENT,
    UserRole.SUPPORT
  ])
}

export const canEditRequest = (userRole: UserRole, isOwner: boolean): boolean => {
  return isAdmin(userRole) || canManageRequests(userRole) || isOwner
}

export const canDeleteRequest = (userRole: UserRole): boolean => {
  return hasRole(userRole, [UserRole.ADMIN, UserRole.PRODUCT_MANAGER])
}

export const canViewInternalComments = (userRole: UserRole): boolean => {
  return hasRole(userRole, [
    UserRole.ADMIN,
    UserRole.PRODUCT_MANAGER,
    UserRole.DEVELOPER,
    UserRole.QA,
    UserRole.OPERATIONS,
    UserRole.SUPPORT,
    UserRole.MANAGEMENT
  ])
}

import Redis from 'ioredis'

const globalForRedis = globalThis as unknown as {
  redis: Redis | undefined
}

export const redis =
  globalForRedis.redis ??
  new Redis(process.env.REDIS_URL || 'redis://localhost:6379', {
    retryDelayOnFailover: 100,
    enableReadyCheck: false,
    maxRetriesPerRequest: null,
  })

if (process.env.NODE_ENV !== 'production') globalForRedis.redis = redis

// Cache utilities
export const cache = {
  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await redis.get(key)
      return value ? JSON.parse(value) : null
    } catch (error) {
      console.error('Cache get error:', error)
      return null
    }
  },

  async set(key: string, value: any, ttl?: number): Promise<void> {
    try {
      const serialized = JSON.stringify(value)
      if (ttl) {
        await redis.setex(key, ttl, serialized)
      } else {
        await redis.set(key, serialized)
      }
    } catch (error) {
      console.error('Cache set error:', error)
    }
  },

  async del(key: string): Promise<void> {
    try {
      await redis.del(key)
    } catch (error) {
      console.error('Cache delete error:', error)
    }
  },

  async exists(key: string): Promise<boolean> {
    try {
      const result = await redis.exists(key)
      return result === 1
    } catch (error) {
      console.error('Cache exists error:', error)
      return false
    }
  },

  async flush(): Promise<void> {
    try {
      await redis.flushall()
    } catch (error) {
      console.error('Cache flush error:', error)
    }
  }
}

// Session utilities
export const session = {
  async get(sessionId: string): Promise<any | null> {
    return cache.get(`session:${sessionId}`)
  },

  async set(sessionId: string, data: any, ttl: number = 3600): Promise<void> {
    await cache.set(`session:${sessionId}`, data, ttl)
  },

  async destroy(sessionId: string): Promise<void> {
    await cache.del(`session:${sessionId}`)
  }
}

// Rate limiting utilities
export const rateLimit = {
  async check(key: string, limit: number, window: number): Promise<{ allowed: boolean; remaining: number }> {
    try {
      const current = await redis.incr(key)
      
      if (current === 1) {
        await redis.expire(key, window)
      }
      
      const remaining = Math.max(0, limit - current)
      const allowed = current <= limit
      
      return { allowed, remaining }
    } catch (error) {
      console.error('Rate limit error:', error)
      return { allowed: true, remaining: limit }
    }
  }
}

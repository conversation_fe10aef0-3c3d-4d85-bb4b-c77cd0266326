"use client"

import { <PERSON>actNode } from "react"
import { <PERSON><PERSON> } from "./header"
import { Sidebar } from "./sidebar"
import { Footer } from "./footer"

interface MainLayoutProps {
  children: ReactNode
  showSidebar?: boolean
}

export function MainLayout({ children, showSidebar = true }: MainLayoutProps) {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="flex">
        {showSidebar && <Sidebar />}
        <main className={`flex-1 ${showSidebar ? "lg:pl-64" : ""}`}>
          <div className="container py-6">
            {children}
          </div>
        </main>
      </div>
      <Footer />
    </div>
  )
}
